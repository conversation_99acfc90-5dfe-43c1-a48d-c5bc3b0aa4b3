import datetime
import uuid

import pytest

from app.integrations.backends.file.file_processor import FileProcessor
from app.integrations.base.chunker import IChunker
from app.integrations.base.document_store import IDocumentStore
from app.integrations.base.embedder import IEmbedder
from app.integrations.base.file_adapter import BaseFileAdapter, FileData
from app.integrations.base.parser import IParser
from app.integrations.schemas import DocumentData, FileProcessingResult
from app.integrations.types import ExtensionType, IntegrationSource


@pytest.fixture
def mock_document_store(mocker):
    store = mocker.Mock(spec=IDocumentStore)
    store.find_document_ids_by_tag = mocker.AsyncMock(return_value=[])
    store.store_document = mocker.AsyncMock()
    return store


@pytest.fixture
def mock_adapter(mocker):
    adapter = mocker.Mock(spec=BaseFileAdapter)
    adapter.list_files = mocker.AsyncMock()
    adapter.download_file = mocker.AsyncMock()
    return adapter


@pytest.fixture
def mock_parser(mocker):
    parser = mocker.AsyncMock(spec=IParser)
    parser.parse = mocker.AsyncMock(return_value="parsed content")
    return parser


@pytest.fixture
def mock_chunker(mocker):
    chunker = mocker.Mock(spec=IChunker)
    chunker.chunk = mocker.Mock(return_value=["chunk1"])
    return chunker


@pytest.fixture
def mock_embedder(mocker):
    embedder = mocker.Mock(spec=IEmbedder)
    embedder.embed_text = mocker.AsyncMock(return_value=[0.1, 0.2, 0.3])
    return embedder


@pytest.fixture
def file_processor(
    mock_document_store,
    mock_adapter,
    mock_parser,
    mock_chunker,
    mock_embedder,
):
    return FileProcessor(
        document_store=mock_document_store,
        source=IntegrationSource.GCS,
        adapter=mock_adapter,
        parser=mock_parser,
        chunker=mock_chunker,
        embedder=mock_embedder,
    )


@pytest.mark.anyio
async def test_process_files_success(file_processor, mock_adapter):
    bucket_name = "test-bucket"
    extensions = [ExtensionType.TXT]
    file_id = str(uuid.uuid4())
    file_name = "test.txt"
    file_size = 100
    time_created = datetime.datetime.now(datetime.UTC)
    last_modified = datetime.datetime.now(datetime.UTC)
    file_md5 = "abc123"

    mock_file = FileData(
        id=file_id,
        name=file_name,
        size=file_size,
        last_modified=last_modified,
        time_created=time_created,
        md5_hash=file_md5,
        content_type="text/plain",
    )
    mock_adapter.list_files.return_value = [mock_file]
    mock_adapter.download_file.return_value = b"test content"

    # Execute
    result = await file_processor.process_files(bucket_name, extensions)

    assert isinstance(result, FileProcessingResult)
    assert result.processed_files == 1
    assert result.deleted_documents == 0

    mock_adapter.list_files.assert_called_once_with(bucket_name, extensions)
    mock_adapter.download_file.assert_called_once_with(bucket_name, file_name)

    file_processor.parser.parse.assert_called_once_with(
        b"test content", "txt", "text/plain"
    )
    file_processor.chunker.chunk.assert_called_once_with("parsed content")

    assert file_processor.embedder.embed_text.call_count == 1
    assert file_processor.document_store.store_document.call_count == 1

    store_calls = file_processor.document_store.store_document.call_args_list
    for i, call in enumerate(store_calls):
        args, kwargs = call
        doc, embedding = args
        assert isinstance(doc, DocumentData)
        assert doc.id == f"{file_id}_chunk_{i}"
        assert doc.content == f"chunk{i + 1}"
        assert doc.source_timestamp == last_modified
        assert doc.tags == {
            f"md5_hash:{file_md5}",
            f"chunk_index:{i}",
        }
        assert embedding == [0.1, 0.2, 0.3]


@pytest.mark.anyio
async def test_process_files_skip_existing(
    file_processor, mock_adapter, mock_document_store
):
    bucket_name = "test-bucket"
    extensions = [ExtensionType.TXT]
    file_id = str(uuid.uuid4())
    file_name = "test.txt"
    file_size = 100
    time_created = datetime.datetime.now(datetime.UTC)
    last_modified = datetime.datetime.now(datetime.UTC)
    file_md5 = "abc123"

    mock_file = FileData(
        id=file_id,
        name=file_name,
        size=file_size,
        time_created=time_created,
        last_modified=last_modified,
        md5_hash=file_md5,
        content_type="text/plain",
    )
    mock_adapter.list_files.return_value = [mock_file]
    mock_document_store.find_document_ids_by_tag.return_value = ["existing_doc"]

    # Execute
    result = await file_processor.process_files(bucket_name, extensions)

    assert isinstance(result, FileProcessingResult)
    assert result.processed_files == 0
    assert result.deleted_documents == 0

    mock_adapter.list_files.assert_called_once_with(bucket_name, extensions)
    mock_adapter.download_file.assert_not_called()

    file_processor.parser.parse.assert_not_called()
    file_processor.chunker.chunk.assert_not_called()
    file_processor.embedder.embed_text.assert_not_called()
    file_processor.document_store.store_document.assert_not_called()


@pytest.mark.anyio
async def test_process_files_adapter_error(file_processor, mock_adapter):
    bucket_name = "test-bucket"
    extensions = [ExtensionType.TXT]
    mock_adapter.list_files.side_effect = Exception("Adapter error")

    with pytest.raises(Exception, match="Adapter error"):
        await file_processor.process_files(bucket_name, extensions)

    mock_adapter.list_files.assert_called_once_with(bucket_name, extensions)
    mock_adapter.download_file.assert_not_called()

    file_processor.parser.parse.assert_not_called()
    file_processor.chunker.chunk.assert_not_called()
    file_processor.embedder.embed_text.assert_not_called()
    file_processor.document_store.store_document.assert_not_called()


@pytest.mark.anyio
async def test_process_files_empty_chunk(file_processor, mock_adapter):
    bucket_name = "test-bucket"
    extensions = [ExtensionType.TXT]

    mock_file = FileData(
        id="test",
        name="test.txt",
        size=100,
        last_modified=datetime.datetime.now(datetime.UTC),
        time_created=datetime.datetime.now(datetime.UTC),
        md5_hash="test",
        content_type="text/plain",
    )

    mock_adapter.list_files.return_value = [mock_file]
    mock_adapter.download_file.return_value = b"test content"

    file_processor.chunker.chunk.return_value = ["", "   ", "\n\t", "  "]

    await file_processor.process_files(bucket_name, extensions)

    file_processor.chunker.chunk.assert_called_once_with("parsed content")
    file_processor.embedder.embed_text.assert_not_called()
