from typing import Any

from app.integrations.backends.file.process_runner import File<PERSON><PERSON><PERSON><PERSON><PERSON>ner
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.context import IntegrationContext
from app.integrations.types import ExtensionType, IntegrationSource


class FileBackend(BaseFileBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseFileAdapter],
        source: IntegrationSource,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )
        self.process_runner = FileProcessRunner(
            context=context,
            source=source,
            adapter_class=adapter_class,
        )

    async def start_processing(
        self,
        bucket_names: list[str],
        extensions: list[ExtensionType],
    ) -> dict[str, Any]:
        return await self.process_runner.run(
            bucket_names=bucket_names,
            extensions=extensions,
        )
