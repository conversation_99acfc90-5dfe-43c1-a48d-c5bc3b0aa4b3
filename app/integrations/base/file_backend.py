from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.types import BackendType, ExtensionType


class BaseFileBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.FILE

    @abstractmethod
    async def start_processing(
        self,
        bucket_names: list[str],
        extensions: list[ExtensionType],
    ) -> dict[str, Any]:
        pass
