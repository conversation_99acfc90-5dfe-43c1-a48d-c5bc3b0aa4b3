import datetime

from pydantic import BaseModel


class MessageData(BaseModel):
    message_id: str
    channel_id: str
    content: str
    sent_at: datetime.datetime
    last_edit_at: datetime.datetime | None = None
    tombstone: bool | None = False
    author: str | None = None
    thread_id: str | None = None
    parent_id: str | None = None


class MessageChangelogData(BaseModel):
    channel_id: str
    operation: str
    message_id: str
    cursor_id: int
    created_at: datetime.datetime


class ChannelDataSlice(BaseModel):
    channel_id: str
    messages: list[MessageData]
    from_time: datetime.datetime
    to_time: datetime.datetime


class ReconciliationStats(BaseModel):
    inserts: int
    updates: int
    deletes: int


class ChannelIngestionResult(BaseModel):
    messages_count: int
    inserts: int
    updates: int
    deletes: int
    from_time: datetime.datetime
    to_time: datetime.datetime


class ChannelProcessingResult(BaseModel):
    processed_changes: int
    regenerated_documents: int
    deleted_documents: int


class DocumentData(BaseModel):
    id: str
    content: str
    source_timestamp: datetime.datetime
    tags: set[str]


class CRMAccountAccessData(BaseModel):
    account_id: str
    account_name: str
    access_type: str
    access_role: str | None


class CRMAccountAccessSlice(BaseModel):
    user_id: str
    accounts: list[CRMAccountAccessData]


class CRMAccountAccessSyncResult(BaseModel):
    new_access_count: int
    old_access_count: int


class FileData(BaseModel):
    id: str
    name: str
    size: int
    time_created: datetime.datetime
    last_modified: datetime.datetime
    md5_hash: str
    content_type: str


class FileProcessingResult(BaseModel):
    processed_files: int
    deleted_documents: int
