from collections.abc import AsyncIterator, Sequence
from typing import Any
from uuid import UUID

from langchain_core.messages import AI<PERSON>essage, BaseMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langfuse.langchain import Callback<PERSON>andler
from langgraph.checkpoint.postgres import CheckpointTuple
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from app.agentic.graph.stream_encoder import DataStreamProtocolEncoder
from app.agentic.graph.stream_output_processor import StreamOutputProcessor
from app.agentic.schemas import (
    FrontendToolCallResult,
    PaginationInfo,
    ThreadHistoryResponse,
    ThreadMessage,
)


class GraphManager:
    def __init__(
        self,
        graph: CompiledStateGraph,
        langfuse_callback_handler: CallbackHandler,
    ):
        self.graph = graph
        self.langfuse_callback_handler = langfuse_callback_handler
        self.stream_output_processor = StreamOutputProcessor(
            DataStreamProtocolEncoder()
        )

    def _get_config(self, thread_id: str, checkpoint_ns: str = "") -> RunnableConfig:
        callbacks = [self.langfuse_callback_handler]
        configurable = {
            "thread_id": thread_id,
            "checkpoint_ns": checkpoint_ns,
        }

        return RunnableConfig(configurable=configurable, callbacks=callbacks)

    async def invoke_graph(self, graph_input: dict[str, Any]) -> dict[str, Any]:
        config = self._get_config(graph_input["thread_id"])
        return await self.graph.ainvoke(input=graph_input, config=config)

    async def stream_graph(
        self,
        messages: Sequence[BaseMessage],
        crm_account_id: str,
        thread_id: str,
        org_id: UUID,
        user_id: UUID,
        resume: FrontendToolCallResult | None = None,
    ) -> AsyncIterator[str]:
        config = self._get_config(thread_id)

        graph_input: dict[str, Any] | Command
        if resume is not None:
            graph_input = Command(resume=resume)
        else:
            graph_input = {
                "messages": messages,
                "crm_account_id": crm_account_id,
                "thread_id": thread_id,
                "org_id": org_id,
                "user_id": user_id,
            }

        async for mode, output in self.graph.astream(
            input=graph_input,
            config=config,
            stream_mode=[
                "messages",  # stream LLM messages
                "updates",  # stream updates including interrupts
            ],
        ):
            chunk = None
            if mode == "messages":
                assert isinstance(output, tuple)
                assert isinstance(output[0], BaseMessage | Command)
                chunk = output[0]
            elif (
                mode == "updates"
                and isinstance(output, dict)
                and "__interrupt__" in output
            ):
                chunk = output["__interrupt__"][0]
                assert isinstance(chunk, Interrupt)

            if chunk:
                async for output in self.stream_output_processor.process(chunk):
                    yield output

    @staticmethod
    def _parse_historical_message_data(
        lc_message: AIMessage | HumanMessage | ToolMessage,
    ) -> dict[str, Any] | None:
        if not hasattr(lc_message, "content"):
            return None

        content_text = str(lc_message.content)

        if not content_text.strip():
            return None

        if isinstance(lc_message, HumanMessage):
            role = "user"
        elif isinstance(lc_message, AIMessage):
            role = "assistant"
        else:
            return None

        parsed_data: dict[str, Any] = {
            "id": lc_message.id,
            "role": role,
            "content": {"type": "text", "text": content_text},
        }

        return parsed_data

    async def _get_latest_checkpoint_messages(
        self, thread_id: str
    ) -> list[AIMessage | HumanMessage | ToolMessage] | None:
        runnable_config = self._get_config(thread_id)
        latest_checkpoint_tuple: CheckpointTuple | None = None

        checkpointer = self.graph.checkpointer
        if not isinstance(checkpointer, AsyncPostgresSaver):
            return None

        async for cpt in checkpointer.alist(config=runnable_config, limit=1):
            latest_checkpoint_tuple = cpt
            break

        if not latest_checkpoint_tuple:
            return None

        checkpoint_content = latest_checkpoint_tuple.checkpoint
        channel_values = checkpoint_content.get("channel_values", {})
        return channel_values.get("messages", [])

    @staticmethod
    def _get_paginated_messages(
        all_messages: list[AIMessage | HumanMessage | ToolMessage],
        page: int,
        size: int,
    ) -> list[AIMessage | HumanMessage | ToolMessage]:
        start_index = (page - 1) * size
        end_index = start_index + size
        return all_messages[start_index:end_index]

    async def get_historical_messages(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse | None:
        all_langchain_messages = await self._get_latest_checkpoint_messages(thread_id)

        if not all_langchain_messages:
            return None

        total_messages = len(all_langchain_messages)
        total_pages = (total_messages + size - 1) // size

        pagination_info = PaginationInfo(
            thread_id=thread_id,
            current_page=page,
            page_size=size,
            total_messages=total_messages,
            total_pages=total_pages,
        )

        paginated_messages = self._get_paginated_messages(
            all_langchain_messages, page, size
        )

        historical_messages = []
        for lc_message in paginated_messages:
            message_data = self._parse_historical_message_data(lc_message)
            if message_data:
                content = message_data["content"]["text"]
                historical_message = ThreadMessage(
                    id=message_data["id"],
                    role=message_data["role"],
                    content=content,
                )
                historical_messages.append(historical_message)

        return ThreadHistoryResponse(
            pagination=pagination_info,
            messages=historical_messages,
        )
