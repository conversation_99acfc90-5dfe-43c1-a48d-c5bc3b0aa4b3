import pytest
from langchain_core.runnables import Runnable
from langchain_core.tools import StructuredTool
from langfuse import Langfuse
from langgraph.graph.state import StateGraph
from langgraph.pregel import Pregel
from pydantic import BaseModel

from app.agentic.graph.graph import GraphFactory
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.agentic.schemas import FrontendToolCallResult


class MockToolSchema(BaseModel):
    """Mock schema for testing tools."""

    test_param: str


@pytest.fixture
def mock_langfuse_client(mocker):
    return mocker.Mock(spec=Langfuse)


@pytest.mark.anyio
async def test_create_graph(mocker, user_id, mock_user_integrations_instance):
    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(return_value=[])

    mock_create_supervisor = mocker.patch("app.agentic.graph.graph.create_supervisor")
    mock_supervisor_agent = mocker.Mock(spec=Runnable)
    mock_create_supervisor.return_value.compile.return_value = mock_supervisor_agent

    mock_create_react_agent = mocker.patch("app.agentic.graph.graph.create_react_agent")
    mock_sales_doc_agent = mocker.Mock(spec=Pregel)
    mock_create_react_agent.return_value = mock_sales_doc_agent

    mock_get_llm = mocker.patch("app.agentic.graph.graph.GraphFactory._get_llm")
    mock_get_llm.return_value = mocker.Mock()

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )

    mock_agent_prompts_instance = mocker.Mock(spec=AgentPrompts)
    mock_agent_prompts_instance.get_supervisor_system_prompt = mocker.AsyncMock(
        return_value=mocker.Mock()
    )
    mock_agent_prompts_instance.get_sales_document_agent_prompt = mocker.AsyncMock(
        return_value=mocker.Mock()
    )
    factory.agent_prompts = mock_agent_prompts_instance

    created_graph = await factory.create_graph()

    assert isinstance(created_graph, StateGraph)
    assert "fetch_account" in created_graph.nodes
    assert "context_injector" in created_graph.nodes
    assert "supervisor" in created_graph.nodes
    assert created_graph.output == ConversationState
    assert created_graph.compiled is False


@pytest.mark.anyio
async def test_get_langchain_tools(mocker, user_id, mock_user_integrations_instance):
    mock_tool_definition = mocker.Mock()
    mock_tool_definition.name = "test_tool"
    mock_tool_definition.description = "Test tool description"
    mock_tool_definition.coroutine = mocker.AsyncMock()
    mock_tool_definition.args_schema = MockToolSchema

    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(
        return_value=[mock_tool_definition]
    )

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )
    tools = await factory.get_langchain_tools()

    assert len(tools) == 1
    assert isinstance(tools[0], StructuredTool)
    assert tools[0].name == "test_tool"
    assert tools[0].description == "Test tool description"


def test_needs_account_refresh():
    state_empty = ConversationState()
    assert GraphFactory._needs_account_refresh(state_empty) is True

    state_no_account = ConversationState(last_refetch_at="2024-01-01")
    assert GraphFactory._needs_account_refresh(state_no_account) is True

    state_no_refetch = ConversationState(account_info={"id": "123"})
    assert GraphFactory._needs_account_refresh(state_no_refetch) is True

    state_complete = ConversationState(
        last_refetch_at="2024-01-01", account_info={"id": "123"}
    )
    assert GraphFactory._needs_account_refresh(state_complete) is False


@pytest.mark.anyio
async def test_create_supervisor_agent(
    mocker, user_id, mock_user_integrations_instance
):
    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(return_value=[])

    mock_create_supervisor = mocker.patch("app.agentic.graph.graph.create_supervisor")
    mock_supervisor_runnable = mocker.Mock(spec=Runnable)
    mock_create_supervisor.return_value.compile.return_value = mock_supervisor_runnable

    mock_get_llm = mocker.patch("app.agentic.graph.graph.GraphFactory._get_llm")
    mock_llm = mocker.Mock()
    mock_get_llm.return_value = mock_llm

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )

    mock_agent_prompts_instance = mocker.Mock(spec=AgentPrompts)
    mock_prompt = mocker.Mock()
    mock_agent_prompts_instance.get_supervisor_system_prompt = mocker.AsyncMock(
        return_value=mock_prompt
    )
    factory.agent_prompts = mock_agent_prompts_instance

    mock_sales_agent = mocker.Mock(spec=Runnable)

    supervisor = await factory._create_supervisor_agent([mock_sales_agent])

    mock_create_supervisor.assert_called_once_with(
        agents=[mock_sales_agent],
        model=mock_llm,
        tools=[],
        prompt=mock_prompt,
        state_schema=ConversationState,
        supervisor_name="supervisor",
    )
    assert supervisor == mock_supervisor_runnable


@pytest.mark.anyio
async def test_create_sales_document_agent(
    mocker, user_id, mock_user_integrations_instance
):
    mock_create_react_agent = mocker.patch("app.agentic.graph.graph.create_react_agent")
    mock_sales_agent = mocker.Mock(spec=Pregel)
    mock_create_react_agent.return_value = mock_sales_agent

    mock_get_llm = mocker.patch("app.agentic.graph.graph.GraphFactory._get_llm")
    mock_llm = mocker.Mock()
    mock_get_llm.return_value = mock_llm

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )

    mock_agent_prompts_instance = mocker.Mock(spec=AgentPrompts)
    mock_prompt = mocker.Mock()
    mock_agent_prompts_instance.get_sales_document_agent_prompt = mocker.AsyncMock(
        return_value=mock_prompt
    )
    factory.agent_prompts = mock_agent_prompts_instance

    sales_agent = await factory._create_sales_document_agent()

    mock_create_react_agent.assert_called_once_with(
        model=mock_llm,
        tools=[],
        prompt=mock_prompt,
        name="sales_document_agent",
        state_schema=ConversationState,
    )
    assert sales_agent == mock_sales_agent


@pytest.mark.anyio
async def test_create_graph_with_conditional_edges(
    mocker, user_id, mock_user_integrations_instance
):
    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(return_value=[])

    mock_create_supervisor = mocker.patch("app.agentic.graph.graph.create_supervisor")
    mock_supervisor_agent = mocker.Mock(spec=Runnable)
    mock_create_supervisor.return_value.compile.return_value = mock_supervisor_agent

    mock_create_react_agent = mocker.patch("app.agentic.graph.graph.create_react_agent")
    mock_sales_doc_agent = mocker.Mock(spec=Pregel)
    mock_create_react_agent.return_value = mock_sales_doc_agent

    mock_get_llm = mocker.patch("app.agentic.graph.graph.GraphFactory._get_llm")
    mock_get_llm.return_value = mocker.Mock()

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )

    mock_agent_prompts_instance = mocker.Mock(spec=AgentPrompts)
    mock_agent_prompts_instance.get_supervisor_system_prompt = mocker.AsyncMock(
        return_value=mocker.Mock()
    )
    mock_agent_prompts_instance.get_sales_document_agent_prompt = mocker.AsyncMock(
        return_value=mocker.Mock()
    )
    factory.agent_prompts = mock_agent_prompts_instance

    created_graph = await factory.create_graph()

    assert len(created_graph.edges) > 0

    edges_dict = {edge[0]: edge[1] for edge in created_graph.edges}
    assert "fetch_account" in edges_dict
    assert edges_dict["fetch_account"] == "context_injector"
    assert "context_injector" in edges_dict
    assert edges_dict["context_injector"] == "supervisor"


@pytest.mark.anyio
async def test_get_langchain_tools_empty_list(
    mocker, user_id, mock_user_integrations_instance
):
    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(return_value=[])

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )
    tools = await factory.get_langchain_tools()

    assert tools == []
    mock_tool_registry.assert_called_once_with(user_id, mock_user_integrations_instance)


@pytest.mark.anyio
async def test_get_langchain_tools_multiple_tools(
    mocker, user_id, mock_user_integrations_instance
):
    mock_tool_def_1 = mocker.Mock()
    mock_tool_def_1.name = "tool_1"
    mock_tool_def_1.description = "First tool"
    mock_tool_def_1.coroutine = mocker.AsyncMock()
    mock_tool_def_1.args_schema = MockToolSchema

    mock_tool_def_2 = mocker.Mock()
    mock_tool_def_2.name = "tool_2"
    mock_tool_def_2.description = "Second tool"
    mock_tool_def_2.coroutine = mocker.AsyncMock()
    mock_tool_def_2.args_schema = MockToolSchema

    mock_tool_registry = mocker.patch("app.agentic.graph.graph.ToolRegistry")
    mock_tool_registry.return_value.get_tools = mocker.AsyncMock(
        return_value=[
            mock_tool_def_1,
            mock_tool_def_2,
        ]
    )

    factory = GraphFactory(
        user_id, mock_user_integrations_instance, mock_langfuse_client
    )
    tools = await factory.get_langchain_tools()

    assert len(tools) == 2
    assert all(isinstance(tool, StructuredTool) for tool in tools)
    assert tools[0].name == "tool_1"
    assert tools[1].name == "tool_2"


@pytest.mark.anyio
async def test_create_langchain_tool_requires_human_review(mocker):
    mock_tool_def = mocker.Mock()
    mock_tool_def.name = "test_tool"
    mock_tool_def.description = "Test tool"
    mock_tool_def.coroutine = mocker.AsyncMock(return_value="original_result")
    mock_tool_def.args_schema = MockToolSchema
    mock_tool_def.requires_human_review = True

    mock_interrupt = mocker.patch("app.agentic.graph.graph.interrupt")
    mock_interrupt.return_value = FrontendToolCallResult(action="continue")

    tool = GraphFactory.create_langchain_tool(mock_tool_def)
    await tool.ainvoke(input={"test_param": "value"})

    mock_interrupt.assert_called_once_with(
        {
            "tool_name": "test_tool",
            "tool_args": {"test_param": "value"},
        }
    )
